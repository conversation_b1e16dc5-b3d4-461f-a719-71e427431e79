import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_extension/ex_paint.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_extension/ex_color_filter.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_extension/ex_image_filter.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_extension/ex_mask_filter.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_extension/ex_color.dart';

void main() {
  group('Paint Extension Tests', () {
    test('Paint toJson and fromJson should work correctly', () {
      final paint = Paint()
        ..color = Colors.red
        ..strokeWidth = 5.0
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..strokeJoin = StrokeJoin.round
        ..isAntiAlias = true
        ..blendMode = BlendMode.srcOver;

      final json = paint.toJson();
      final restoredPaint = jsonToPaint(json);

      expect(restoredPaint.color, equals(paint.color));
      expect(restoredPaint.strokeWidth, equals(paint.strokeWidth));
      expect(restoredPaint.style, equals(paint.style));
      expect(restoredPaint.strokeCap, equals(paint.strokeCap));
      expect(restoredPaint.strokeJoin, equals(paint.strokeJoin));
      expect(restoredPaint.isAntiAlias, equals(paint.isAntiAlias));
      expect(restoredPaint.blendMode, equals(paint.blendMode));
    });

    test('Paint copyWith should work correctly', () {
      final originalPaint = Paint()
        ..color = Colors.blue
        ..strokeWidth = 3.0;

      final copiedPaint = originalPaint.copyWith(
        color: Colors.red,
        strokeWidth: 5.0,
      );

      expect(copiedPaint.color, equals(Colors.red));
      expect(copiedPaint.strokeWidth, equals(5.0));
      // 原始Paint不应该被修改
      expect(originalPaint.color, equals(Colors.blue));
      expect(originalPaint.strokeWidth, equals(3.0));
    });

    test('jsonToPaint should handle invalid data gracefully', () {
      // 测试空数据
      final paint1 = jsonToPaint(<String, dynamic>{});
      expect(paint1.color, equals(const Color(0xFF000000)));
      expect(paint1.strokeWidth, equals(1.0));

      // 测试无效的枚举索引
      final paint2 = jsonToPaint(<String, dynamic>{
        'blendMode': 999, // 无效索引
        'color': 0xFF0000FF,
        'strokeWidth': 2.0,
      });
      expect(paint2.blendMode, equals(BlendMode.srcOver)); // 应该使用默认值
      expect(paint2.color, equals(const Color(0xFF0000FF)));
      expect(paint2.strokeWidth, equals(2.0));

      // 测试错误的数据类型
      final paint3 = jsonToPaint(<String, dynamic>{
        'color': 'invalid_color',
        'strokeWidth': 'invalid_width',
        'isAntiAlias': 'not_a_bool',
      });
      expect(paint3.color, equals(const Color(0xFF000000))); // 默认颜色
      expect(paint3.strokeWidth, equals(1.0)); // 默认宽度
      expect(paint3.isAntiAlias, equals(false)); // 默认值
    });
  });

  group('ColorFilter Extension Tests', () {
    test('stringToColorFilter should parse mode filter correctly', () {
      const colorFilter = ColorFilter.mode(Colors.red, BlendMode.multiply);
      final filterString = colorFilter.toString();
      final parsedFilter = stringToColorFilter(filterString);
      
      expect(parsedFilter, isNotNull);
      // 注意：由于ColorFilter没有直接的相等比较，我们只能检查是否成功解析
    });

    test('stringToColorFilter should handle invalid input gracefully', () {
      expect(stringToColorFilter('invalid_filter'), isNull);
      expect(stringToColorFilter('ColorFilter.mode(invalid)'), isNull);
      expect(stringToColorFilter('ColorFilter.matrix(1,2,3)'), isNull); // 不足20个元素
    });

    test('stringToColorFilter should parse gamma filters correctly', () {
      const linearFilter = ColorFilter.linearToSrgbGamma();
      const srgbFilter = ColorFilter.srgbToLinearGamma();
      
      final parsedLinear = stringToColorFilter(linearFilter.toString());
      final parsedSrgb = stringToColorFilter(srgbFilter.toString());
      
      expect(parsedLinear, isNotNull);
      expect(parsedSrgb, isNotNull);
    });
  });

  group('ImageFilter Extension Tests', () {
    test('stringToImageFilter should parse blur filter correctly', () {
      final blurFilter = ImageFilter.blur(sigmaX: 2.0, sigmaY: 3.0);
      final filterString = blurFilter.toString();
      final parsedFilter = stringToImageFilter(filterString);
      
      expect(parsedFilter, isNotNull);
    });

    test('stringToImageFilter should handle invalid input gracefully', () {
      expect(stringToImageFilter('invalid_filter'), isNull);
      expect(stringToImageFilter('ImageFilter.blur(invalid)'), isNull);
      expect(stringToImageFilter('ImageFilter.blur(-1, 2, TileMode.clamp)'), isNull); // 负值
    });

    test('stringToImageFilter should parse dilate and erode filters correctly', () {
      final dilateFilter = ImageFilter.dilate(radiusX: 1.0, radiusY: 2.0);
      final erodeFilter = ImageFilter.erode(radiusX: 1.5, radiusY: 2.5);
      
      final parsedDilate = stringToImageFilter(dilateFilter.toString());
      final parsedErode = stringToImageFilter(erodeFilter.toString());
      
      expect(parsedDilate, isNotNull);
      expect(parsedErode, isNotNull);
    });
  });

  group('MaskFilter Extension Tests', () {
    test('stringToMaskFilter should parse blur filter correctly', () {
      final maskFilter = MaskFilter.blur(BlurStyle.normal, 2.0);
      final filterString = maskFilter.toString();
      final parsedFilter = stringToMaskFilter(filterString);
      
      expect(parsedFilter, isNotNull);
    });

    test('stringToMaskFilter should handle invalid input gracefully', () {
      expect(stringToMaskFilter('invalid_filter'), isNull);
      expect(stringToMaskFilter('MaskFilter.blur(invalid)'), isNull);
      expect(stringToMaskFilter('MaskFilter.blur(BlurStyle.normal, -1)'), isNull); // 负值
    });
  });

  group('Color Extension Tests', () {
    test('stringToColor should parse Color correctly', () {
      const color = Color(0xFF123456);
      final colorString = color.toString();
      final parsedColor = stringToColor(colorString);
      
      expect(parsedColor, equals(color));
    });

    test('stringToColor should handle invalid input gracefully', () {
      expect(stringToColor('invalid_color'), isNull);
      expect(stringToColor('Color(invalid)'), isNull);
      expect(stringToColor('Color(0xZZ123456)'), isNull); // 无效十六进制
    });

    test('stringToColor should parse ColorSwatch correctly', () {
      const colorSwatch = Colors.red;
      final swatchString = colorSwatch.toString();
      final parsedColor = stringToColor(swatchString);
      
      expect(parsedColor, isNotNull);
    });
  });
}
