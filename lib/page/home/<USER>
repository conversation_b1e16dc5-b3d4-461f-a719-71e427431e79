


import 'dart:convert';

import 'package:easy_white_board/common/config/build_config.dart';
import 'package:easy_white_board/common/config/router_manager.dart';
import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/page/home/<USER>/drawing_item_widget.dart';
import 'package:easy_white_board/provider/business/drawing_data_provider.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../model/db/drawing_data.dart';
import '../../utils/image_helper.dart';
import '../../widgets/common/base_scaffold.dart';
import 'home_controller.dart';

class HomePage extends ConsumerStatefulWidget{
  const HomePage({super.key});

  @override
  ConsumerState createState() => _HomePageState();

}

class _HomePageState extends ConsumerState<HomePage>{

  late HomeController _controller;

  @override
  void initState() {
    _controller=HomeController(ref);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<DrawingData> datas= ref.watch(drawingDataProvider);
    return BaseScaffold(
        body: Container(
          margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight+30.h),
          child: Column(
            children: [
              _headIntro(),
              Expanded(child: _drawingList(datas))
            ],
          ),
        ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 34.h),
        child: InkWell(
          onTap: () async {
             toDrawingBoardPage();
          },
          child: Container(
            padding: EdgeInsets.all(10.w),
            decoration:   const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle
            ),
            child: Icon(Icons.add,size: 40.w,color: Colors.white,),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
    );
  }

  Widget _headIntro(){
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 80.h,bottom: 20.h,left: 20.w,right: 20.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(context.string.welcomeToUse, style: TextStyle(fontSize: 28.sp),),
          Text(BuildConfig.appName, style: TextStyle(fontSize: 28.sp),),
        ],
      ),
    );
  }

  void toDrawingBoardPage({int? id}){
    toPage(RouteName.drawingBoardPage,params: id!=null?{
      'id': id.toString()
    } : null, replace: true,clearStack: true,
      transition: TransitionType.custom,
      transitionBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child,
          ) =>
          Align(
            child: SizeTransition(
              sizeFactor: animation,
              child: child,
            ),
          ),
    );
  }

  Widget _drawingList(List<DrawingData> datas){
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, //每行的列数
        childAspectRatio: 1, //显示区域宽高相等
      ),
      padding: EdgeInsets.zero,
      itemCount: datas.length ,
      itemBuilder: (context, index) {
        DrawingData  d = datas[index];
        return DrawingItemWidget(data: d,controller: _controller);
      },
    );
  }
}