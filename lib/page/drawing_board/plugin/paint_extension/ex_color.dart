/*
  @override
  String toString() => 'Color(0x${value.toRadixString(16).padLeft(8, '0')})';

  @override
  String toString() => '${objectRuntimeType(this, 'ColorSwatch')}(primary value: ${super.toString()})';
*/

import 'dart:ui';

Color? stringToColor(String data) {
  try {
    if (data.startsWith('Color(0x')) {
      final int openParen = data.indexOf('(0x');
      final int closeParen = data.indexOf(')', openParen);

      if (openParen == -1 || closeParen == -1 || closeParen <= openParen + 3) {
        return null;
      }

      final String hexStr = data.substring(openParen + 3, closeParen);
      final int? colorValue = int.tryParse(hexStr, radix: 16);

      if (colorValue == null) {
        return null;
      }

      return Color(colorValue);
    } else if (data.startsWith('ColorSwatch')) {
      final int primaryIndex = data.indexOf('primary value: ');
      if (primaryIndex == -1) {
        return null;
      }

      final String primaryValue = data.substring(primaryIndex + 15);
      final int closeParen = primaryValue.lastIndexOf(')');

      if (closeParen != -1) {
        return stringToColor(primaryValue.substring(0, closeParen + 1));
      }

      return stringToColor(primaryValue);
    }

    return null;
  } catch (e) {
    // 记录错误但不抛出异常
    return null;
  }
}
