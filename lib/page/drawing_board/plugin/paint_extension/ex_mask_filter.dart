/*
  final BlurStyle _style;
  final double _sigma;

  @override
  String toString() => 'MaskFilter.blur($_style, ${_sigma.toStringAsFixed(1)})';
*/

import 'dart:ui';

import 'package:easy_white_board/log/log.dart';

import '../helper/ex_enum.dart';

MaskFilter? stringToMaskFilter(String data) {
  try {
    if (!data.startsWith('MaskFilter.blur(')) {
      return null;
    }

    final int openParen = data.indexOf('(');
    final int comma = data.indexOf(',');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || comma == -1 || closeParen == -1 || comma <= openParen || closeParen <= comma) {
      return null;
    }

    final String style = data.substring(openParen + 1, comma).trim();
    final String sigmaStr = data.substring(comma + 1, closeParen).trim();

    final BlurStyle? blurStyle = ExEnum.tryParse<BlurStyle>(BlurStyle.values, style);
    if (blurStyle == null) {
      return null;
    }

    final double? sigma = double.tryParse(sigmaStr);
    if (sigma == null || sigma < 0) {
      return null;
    }

    return MaskFilter.blur(blurStyle, sigma);
  } catch (e,stack) {
    // 记录错误但不抛出异常
     Logger.error('tryParse enum error:$e',stack);
    return null;
  }
}
