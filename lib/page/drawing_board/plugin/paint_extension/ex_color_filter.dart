import 'dart:ui';

import 'package:flutter/material.dart';
import '../helper/ex_enum.dart';

import 'ex_color.dart';

/*
  @override
  String toString() {
    switch (_type) {
      case _kTypeMode:
        return 'ColorFilter.mode($_color, $_blendMode)';
      case _kTypeMatrix:
        return 'ColorFilter.matrix($_matrix)';
      case _kTypeLinearToSrgbGamma:
        return 'ColorFilter.linearToSrgbGamma()';
      case _kTypeSrgbToLinearGamma:
        return 'ColorFilter.srgbToLinearGamma()';
      default:
        return 'Unknown ColorFilter type. This is an error. If you\'re seeing this, please file an issue at https://github.com/flutter/flutter/issues/new.';
    }
  }

*/

ColorFilter? stringToColorFilter(String data) {
  try {
    if (data == 'ColorFilter.linearToSrgbGamma()') {
      return const ColorFilter.linearToSrgbGamma();
    } else if (data == 'ColorFilter.srgbToLinearGamma()') {
      return const ColorFilter.srgbToLinearGamma();
    } else if (data.startsWith('ColorFilter.mode(')) {
      return _parseColorFilterMode(data);
    } else if (data.startsWith('ColorFilter.matrix(')) {
      return _parseColorFilterMatrix(data);
    }

    return null;
  } catch (e) {
    // 记录错误但不抛出异常
    return null;
  }
}

ColorFilter? _parseColorFilterMode(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || closeParen == -1 || closeParen <= openParen) {
      return null;
    }

    final String content = data.substring(openParen + 1, closeParen);
    final List<String> parts = content.split(',');

    if (parts.length != 2) {
      return null;
    }

    final Color? color = stringToColor(parts[0].trim());
    final BlendMode? mode = ExEnum.tryParse<BlendMode>(BlendMode.values, parts[1].trim());

    if (color != null && mode != null) {
      return ColorFilter.mode(color, mode);
    }

    return null;
  } catch (e) {
    return null;
  }
}

ColorFilter? _parseColorFilterMatrix(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || closeParen == -1 || closeParen <= openParen) {
      return null;
    }

    final String matrixStr = data.substring(openParen + 1, closeParen);
    final List<String> matrixParts = matrixStr.split(',');

    if (matrixParts.length != 20) { // ColorMatrix应该有20个元素
      return null;
    }

    final List<double> matrixList = <double>[];
    for (final String part in matrixParts) {
      final double? value = double.tryParse(part.trim());
      if (value == null) {
        return null;
      }
      matrixList.add(value);
    }

    return ColorFilter.matrix(matrixList);
  } catch (e) {
    return null;
  }
}
