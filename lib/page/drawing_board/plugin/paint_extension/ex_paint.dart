import 'dart:ui';

import 'ex_color_filter.dart';
import 'ex_image_filter.dart';
import 'ex_mask_filter.dart';

/// 为`Paint`扩展`copyWith`
extension ExPaint on Paint {
  Paint copyWith({
    BlendMode? blendMode,
    Color? color,
    ColorFilter? colorFilter,
    FilterQuality? filterQuality,
    ImageFilter? imageFilter,
    bool? invertColors,
    bool? isAntiAlias,
    MaskFilter? maskFilter,
    Shader? shader,
    StrokeCap? strokeCap,
    StrokeJoin? strokeJoin,
    double? strokeWidth,
    PaintingStyle? style,
  }) {
    return Paint()
      ..blendMode = blendMode ?? this.blendMode
      ..color = color ?? this.color
      ..colorFilter = colorFilter ?? this.colorFilter
      ..filterQuality = filterQuality ?? this.filterQuality
      ..imageFilter = imageFilter ?? this.imageFilter
      ..invertColors = invertColors ?? this.invertColors
      ..isAntiAlias = isAntiAlias ?? this.isAntiAlias
      ..maskFilter = maskFilter ?? this.maskFilter
      ..shader = shader ?? this.shader
      ..strokeCap = strokeCap ?? this.strokeCap
      ..strokeJoin = strokeJoin ?? this.strokeJoin
      ..strokeWidth = strokeWidth ?? this.strokeWidth
      ..style = style ?? this.style;
  }

  Map<String, dynamic> toJson() {
    try {
      final Map<String, dynamic> result = <String, dynamic>{
        'blendMode': blendMode.index,
        'color': color.value, // TODO: 考虑使用color.toARGB32()替代
        'filterQuality': filterQuality.index,
        'invertColors': invertColors,
        'isAntiAlias': isAntiAlias,
        'strokeCap': strokeCap.index,
        'strokeJoin': strokeJoin.index,
        'strokeWidth': strokeWidth,
        'style': style.index,
      };

      // 安全地添加可选字段
      if (colorFilter != null) {
        try {
          result['colorFilter'] = colorFilter.toString();
        } catch (e) {
          // 如果colorFilter序列化失败，跳过该字段
        }
      }

      if (imageFilter != null) {
        try {
          result['imageFilter'] = imageFilter.toString();
        } catch (e) {
          // 如果imageFilter序列化失败，跳过该字段
        }
      }

      if (maskFilter != null) {
        try {
          result['maskFilter'] = maskFilter.toString();
        } catch (e) {
          // 如果maskFilter序列化失败，跳过该字段
        }
      }

      // shader暂时不支持序列化
      // if (shader != null) {
      //   try {
      //     result['shader'] = shader.toString();
      //   } catch (e) {
      //     // shader序列化复杂，暂时跳过
      //   }
      // }

      return result;
    } catch (e) {
      // 如果整个序列化过程失败，返回基本的Paint信息
      return <String, dynamic>{
        'blendMode': BlendMode.srcOver.index,
        'color': const Color(0xFF000000).value, // TODO: 考虑使用color.toARGB32()替代
        'filterQuality': FilterQuality.high.index,
        'invertColors': false,
        'isAntiAlias': false,
        'strokeCap': StrokeCap.round.index,
        'strokeJoin': StrokeJoin.round.index,
        'strokeWidth': 1.0,
        'style': PaintingStyle.stroke.index,
      };
    }
  }
}

Paint jsonToPaint(Map<String, dynamic> data) {
  try {
    final Paint paint = Paint();

    // 安全地设置每个属性，提供默认值
    paint.blendMode = _safeGetBlendMode(data['blendMode']);
    paint.color = _safeGetColor(data['color']);
    paint.colorFilter = _safeGetColorFilter(data['colorFilter']);
    paint.filterQuality = _safeGetFilterQuality(data['filterQuality']);
    paint.imageFilter = _safeGetImageFilter(data['imageFilter']);
    paint.invertColors = _safeGetBool(data['invertColors'], false);
    paint.isAntiAlias = _safeGetBool(data['isAntiAlias'], false);
    paint.maskFilter = _safeGetMaskFilter(data['maskFilter']);
    paint.strokeCap = _safeGetStrokeCap(data['strokeCap']);
    paint.strokeJoin = _safeGetStrokeJoin(data['strokeJoin']);
    paint.strokeWidth = _safeGetDouble(data['strokeWidth'], 1.0);
    paint.style = _safeGetPaintingStyle(data['style']);

    return paint;
  } catch (e) {
    // 如果解析失败，返回默认Paint
    return Paint()
      ..blendMode = BlendMode.srcOver
      ..color = const Color(0xFF000000)
      ..filterQuality = FilterQuality.high
      ..invertColors = false
      ..isAntiAlias = false
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
  }
}

BlendMode _safeGetBlendMode(dynamic value) {
  if (value is int && value >= 0 && value < BlendMode.values.length) {
    return BlendMode.values[value];
  }
  return BlendMode.srcOver;
}

Color _safeGetColor(dynamic value) {
  if (value is int) {
    return Color(value);
  }
  return const Color(0xFF000000);
}

ColorFilter? _safeGetColorFilter(dynamic value) {
  if (value is String) {
    return stringToColorFilter(value);
  }
  return null;
}

FilterQuality _safeGetFilterQuality(dynamic value) {
  if (value is int && value >= 0 && value < FilterQuality.values.length) {
    return FilterQuality.values[value];
  }
  return FilterQuality.high;
}

ImageFilter? _safeGetImageFilter(dynamic value) {
  if (value is String) {
    return stringToImageFilter(value);
  }
  return null;
}

bool _safeGetBool(dynamic value, bool defaultValue) {
  if (value is bool) {
    return value;
  }
  return defaultValue;
}

MaskFilter? _safeGetMaskFilter(dynamic value) {
  if (value is String) {
    return stringToMaskFilter(value);
  }
  return null;
}

StrokeCap _safeGetStrokeCap(dynamic value) {
  if (value is int && value >= 0 && value < StrokeCap.values.length) {
    return StrokeCap.values[value];
  }
  return StrokeCap.round;
}

StrokeJoin _safeGetStrokeJoin(dynamic value) {
  if (value is int && value >= 0 && value < StrokeJoin.values.length) {
    return StrokeJoin.values[value];
  }
  return StrokeJoin.round;
}

double _safeGetDouble(dynamic value, double defaultValue) {
  if (value is double) {
    return value;
  } else if (value is int) {
    return value.toDouble();
  } else if (value is String) {
    return double.tryParse(value) ?? defaultValue;
  }
  return defaultValue;
}

PaintingStyle _safeGetPaintingStyle(dynamic value) {
  if (value is int && value >= 0 && value < PaintingStyle.values.length) {
    return PaintingStyle.values[value];
  }
  return PaintingStyle.stroke;
}
