/*
  @override
  String toString() => 'ImageFilter.blur($sigmaX, $sigmaY, $_modeString)';
  @override
  String toString() => 'ImageFilter.dilate($radiusX, $radiusY)';
  @override
  String toString() => 'ImageFilter.erode($radiusX, $radiusY)';
  @override
  String toString() => 'ImageFilter.compose(source -> $_shortDescription -> result)';
  @override
  String toString() => 'ImageFilter.matrix($data, $filterQuality)';
*/

import 'dart:typed_data';
import 'dart:ui';

import 'package:easy_white_board/log/log.dart';

import '../helper/ex_enum.dart';

ImageFilter? stringToImageFilter(String data) {
  try {
    if (data.startsWith('ImageFilter.blur(')) {
      return _parseImageFilterBlur(data);
    } else if (data.startsWith('ImageFilter.dilate(')) {
      return _parseImageFilterDilate(data);
    } else if (data.startsWith('ImageFilter.erode(')) {
      return _parseImageFilterErode(data);
    } else if (data.startsWith('ImageFilter.compose(')) {
      return _parseImageFilterCompose(data);
    } else if (data.startsWith('ImageFilter.matrix(')) {
      return _parseImageFilterMatrix(data);
    }

    return null;
  } catch (e,stack) {
    // 记录错误但不抛出异常
     Logger.error('tryParse enum error:$e',stack);
    return null;
  }
}

ImageFilter? _parseImageFilterBlur(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || closeParen == -1 || closeParen <= openParen) {
      return null;
    }

    final String content = data.substring(openParen + 1, closeParen);
    final List<String> parts = content.split(',');

    if (parts.length != 3) {
      return null;
    }

    final double? sigmaX = double.tryParse(parts[0].trim());
    final double? sigmaY = double.tryParse(parts[1].trim());
    final TileMode? tileMode = ExEnum.tryParse<TileMode>(TileMode.values, parts[2].trim());

    if (sigmaX == null || sigmaY == null || sigmaX < 0 || sigmaY < 0) {
      return null;
    }

    return ImageFilter.blur(
      sigmaX: sigmaX,
      sigmaY: sigmaY,
      tileMode: tileMode ?? TileMode.clamp,
    );
  } catch (e) {
    return null;
  }
}

ImageFilter? _parseImageFilterDilate(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || closeParen == -1 || closeParen <= openParen) {
      return null;
    }

    final String content = data.substring(openParen + 1, closeParen);
    final List<String> parts = content.split(',');

    if (parts.length != 2) {
      return null;
    }

    final double? radiusX = double.tryParse(parts[0].trim());
    final double? radiusY = double.tryParse(parts[1].trim());

    if (radiusX == null || radiusY == null || radiusX < 0 || radiusY < 0) {
      return null;
    }

    return ImageFilter.dilate(
      radiusX: radiusX,
      radiusY: radiusY,
    );
  } catch (e) {
    return null;
  }
}

ImageFilter? _parseImageFilterErode(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int closeParen = data.lastIndexOf(')');

    if (openParen == -1 || closeParen == -1 || closeParen <= openParen) {
      return null;
    }

    final String content = data.substring(openParen + 1, closeParen);
    final List<String> parts = content.split(',');

    if (parts.length != 2) {
      return null;
    }

    final double? radiusX = double.tryParse(parts[0].trim());
    final double? radiusY = double.tryParse(parts[1].trim());

    if (radiusX == null || radiusY == null || radiusX < 0 || radiusY < 0) {
      return null;
    }

    return ImageFilter.erode(
      radiusX: radiusX,
      radiusY: radiusY,
    );
  } catch (e) {
    return null;
  }
}

ImageFilter? _parseImageFilterCompose(String data) {
  try {
    // ImageFilter.compose的toString格式比较复杂，这里简化处理
    // 实际的compose解析可能需要更复杂的逻辑
    return null;
  } catch (e) {
    return null;
  }
}

ImageFilter? _parseImageFilterMatrix(String data) {
  try {
    final int openParen = data.indexOf('(');
    final int firstCloseParen = data.indexOf(')', openParen);
    final int lastComma = data.lastIndexOf(',');
    final int lastCloseParen = data.lastIndexOf(')');

    if (openParen == -1 || firstCloseParen == -1 || lastComma == -1 || lastCloseParen == -1) {
      return null;
    }

    final String matrixStr = data.substring(openParen + 1, firstCloseParen);
    final String filterQualityStr = data.substring(lastComma + 1, lastCloseParen).trim();

    final List<String> matrixParts = matrixStr.split(',');
    if (matrixParts.length != 16) { // 4x4 matrix
      return null;
    }

    final List<double> matrixListData = <double>[];
    for (final String part in matrixParts) {
      final double? value = double.tryParse(part.trim());
      if (value == null) {
        return null;
      }
      matrixListData.add(value);
    }

    final Float64List matrixList = Float64List.fromList(matrixListData);
    final FilterQuality? filterQuality = ExEnum.tryParse<FilterQuality>(FilterQuality.values, filterQualityStr);

    return ImageFilter.matrix(
      matrixList,
      filterQuality: filterQuality ?? FilterQuality.low,
    );
  } catch (e) {
    return null;
  }
}
