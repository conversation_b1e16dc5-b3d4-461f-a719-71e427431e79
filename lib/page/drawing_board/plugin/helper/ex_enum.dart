import 'package:easy_white_board/log/log.dart';

/// enum解析工具
/// tools of convert enum
class ExEnum {
  const ExEnum._();

  static bool _isEnum<T>(Object item) {
    final List<String> splitItem = item.toString().split('.');
    return splitItem.length > 1 && splitItem[0] == T.toString();
  }

  static T? tryParse<T extends Object>(List<T> values, String? item) {
    if (item == null || item.isEmpty) {
      return null;
    }

    try {
      // 首先尝试直接匹配
      for (final T value in values) {
        if (value.toString() == item) {
          return value;
        }
      }

      // 如果直接匹配失败，检查是否为枚举格式
      if (_isEnum<T>(item)) {
        for (final T value in values) {
          if (value.toString() == item) {
            return value;
          }
        }
      }

      return null;
    } catch (e, stack) {
      Logger.error('tryParse enum error:$e',stack);
      // 记录错误但不抛出异常，返回null
      return null;
    }
  }
}
