import 'dart:math';
import 'dart:math' as math;
import 'dart:ui';
import 'package:easy_white_board/log/log.dart';

import '../paint_extension/ex_offset.dart';
import '../paint_extension/ex_paint.dart';
import 'paint_content.dart';

class TimeOffset {
  Offset point;

  late int timestamp;

  double width;

  TimeOffset(
      {required this.point, required this.timestamp, required this.width});

  TimeOffset.fromOffset({required this.point, required this.width}) {
    timestamp = DateTime.now().millisecondsSinceEpoch;
  }

  factory TimeOffset.fromJson(Map<String, dynamic> data) {
    return TimeOffset(
      point: jsonToOffset(data['point'] as Map<String, dynamic>),
      timestamp: data['timestamp'],
      width: data['width'],
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'point': point.toJson(),
      'timestamp': timestamp,
      'width': width,
    };
  }
}

/// 笔触线条
class SmoothLineNew extends PaintContent {
  //
  static const double minWidth = 3;

//未使用
  static const double defaultBrushPrecision = 9;

  SmoothLineNew({
    /// 绘制影响因子，值越小线条越平滑，粗细变化越慢
    this.brushPrecision = defaultBrushPrecision,
  });

  SmoothLineNew.data({
    this.brushPrecision = defaultBrushPrecision,
    required this.points,
    required Paint paint,
  }) : super.paint(paint);

  factory SmoothLineNew.fromJson(Map<String, dynamic> data) {
    return SmoothLineNew.data(
      brushPrecision: data['brushPrecision'] as double,
      points: (data['points'] as List<dynamic>)
          .map((dynamic e) => TimeOffset.fromJson(e as Map<String, dynamic>))
          .toList(),
      paint: jsonToPaint(data['paint'] as Map<String, dynamic>),
    );
  }

  final double brushPrecision;

  /// 绘制点列表
  late List<TimeOffset> points;

  @override
  void startDraw(Offset startPoint) {
    points = <TimeOffset>[
      TimeOffset.fromOffset(point: startPoint, width: paint.strokeWidth * 0.7)
    ];
  }

  double moveSpeed(TimeOffset nowPoint, TimeOffset prePoint) {
    final double distance = (nowPoint.point - prePoint.point).distance;
    return (0 == distance)
        ? 0
        : distance / (nowPoint.timestamp - prePoint.timestamp);
  }

  // double lineWidth(
  //     TimeOffset prePoint, Offset nowPoint, double step, int nowTimestamp) {
  //   double maxSpeed = 2;
  //   double distance = (nowPoint - prePoint.point).distance;
  //   double s = distance / (nowTimestamp - prePoint.timestamp);
  //   s = s > maxSpeed ? maxSpeed : s;
  //   double w = (maxSpeed - s) / maxSpeed;
  //   double maxDif = distance * step;
  //   if (w < 0.04) {
  //     w = 0.04;
  //   }
  //   if ((w - prePoint.width).abs() > maxDif) {
  //     if (w > prePoint.width) {
  //       w = prePoint.width + maxDif;
  //     } else {
  //       w = prePoint.width - maxDif;
  //     }
  //   }
  //   return w;
  // }

  double lineWidth(List<TimeOffset> historyPoints, Offset nowPoint, double step,
      int nowTimestamp,
      {double smoothingFactor = 0.2,
      double maxSpeed = 2.0,
      double minWidth = 2,
      int smoothingSamples = 3}) {
    if (historyPoints.isEmpty) return minWidth;

    TimeOffset prePoint = historyPoints.last;

    // 1. 计算多点平均速度
    double avgSpeed = _calculateAverageSpeed(
        historyPoints, nowPoint, nowTimestamp, smoothingSamples);

    // 2. 限制最大速度
    avgSpeed = math.min(avgSpeed, maxSpeed);

    // 3. 速度到宽度的非线性映射
    double rawWidth = _speedToWidth(avgSpeed, maxSpeed);

    // 4. 指数移动平均平滑
    double smoothedWidth =
        prePoint.width * (1 - smoothingFactor) + rawWidth * smoothingFactor;

    // 5. 应用最小宽度
    smoothedWidth = math.max(smoothedWidth, minWidth);

    // 6. 限制变化幅度（更宽松的限制）
    double maxChange = _calculateMaxChange(historyPoints, step);
    smoothedWidth = _clampWidthChange(prePoint.width, smoothedWidth, maxChange);

    return smoothedWidth;
  }

  /// 计算多点平均速度，减少单点噪声
  /// [historyPoints] 历史点列表
  /// [nowPoint] 当前点坐标
  /// [nowTimestamp] 当前时间戳
  /// [sampleCount] 采样点数量
  double _calculateAverageSpeed(List<TimeOffset> historyPoints, Offset nowPoint,
      int nowTimestamp, int sampleCount) {
    if (historyPoints.isEmpty) return 0;

    // 取最近几个点计算平均速度
    int samples = math.min(sampleCount, historyPoints.length);
    double totalSpeed = 0;
    int validSamples = 0;

    // 计算历史点之间的速度
    for (int i = historyPoints.length - samples;
        i < historyPoints.length - 1;
        i++) {
      double distance =
          (historyPoints[i + 1].point - historyPoints[i].point).distance;
      int timeDiff =
          historyPoints[i + 1].timestamp - historyPoints[i].timestamp;
      if (timeDiff > 0) {
        totalSpeed += distance / timeDiff;
        validSamples++;
      }
    }

    // 加上当前点到最后一个历史点的速度
    if (historyPoints.isNotEmpty) {
      TimeOffset lastPoint = historyPoints.last;
      double distance = (nowPoint - lastPoint.point).distance;
      int timeDiff = nowTimestamp - lastPoint.timestamp;
      if (timeDiff > 0) {
        totalSpeed += distance / timeDiff;
        validSamples++;
      }
    }

    return validSamples > 0 ? totalSpeed / validSamples : 0;
  }

  double _speedToWidth(double speed, double maxSpeed) {
    // 使用平方根函数创建更自然的宽度变化
    double normalized = speed / maxSpeed;
    return 1.0 - math.sqrt(normalized);
  }

  double _calculateMaxChange(List<TimeOffset> points, double step) {
    if (points.length < 2) return step;

    // 根据最近几个点的距离动态调整最大变化
    double avgDistance = 0;
    int samples = math.min(3, points.length - 1);

    for (int i = points.length - samples; i < points.length - 1; i++) {
      avgDistance += (points[i + 1].point - points[i].point).distance;
    }

    avgDistance /= samples;
    return avgDistance * step * 0.3; // 更宽松的变化限制
  }

  /// 限制宽度变化幅度，确保线条平滑过渡
  /// [previousWidth] 前一个点的宽度
  /// [targetWidth] 目标宽度
  /// [maxChange] 允许的最大变化量
  /// 返回经过限制的宽度值
  double _clampWidthChange(
      double previousWidth, double targetWidth, double maxChange) {
    // 计算宽度变化量
    double widthDifference = targetWidth - previousWidth;

    // 如果变化量在允许范围内，直接返回目标宽度
    if (widthDifference.abs() <= maxChange) {
      return targetWidth;
    }

    // 如果变化量过大，则限制变化幅度
    if (widthDifference > 0) {
      // 目标宽度比前一个宽度大，限制增加量
      return previousWidth + maxChange;
    } else {
      // 目标宽度比前一个宽度小，限制减少量
      return previousWidth - maxChange;
    }
  }

  @override
  void drawing(Offset nowPoint) {
    var nowTimestamp = DateTime.now().millisecondsSinceEpoch;
    var preTimePoint = points.last;
    if (nowTimestamp - preTimePoint.timestamp < 30) {
      return;
    }
    final double distance = (nowPoint - points.last.point).distance;
    if (distance > 0.02) {
      double step = points.length > 4 ? 0.05 : 0.2;

      // 使用新的改进版lineWidth方法
      double calculatedWidth = lineWidth(
        points, // 传入历史点列表
        nowPoint, // 当前点
        step, // 步长
        nowTimestamp, // 当前时间戳
      );

      // 与前一个点的宽度进行平均，进一步平滑
      // double strokeWidth = (calculatedWidth + preTimePoint.width) / 2;
      double strokeWidth = calculatedWidth;

      //记录点位
      points.add(TimeOffset(
          point: nowPoint, timestamp: nowTimestamp, width: strokeWidth));
    }
  }

  @override
  void draw(Canvas canvas, Size size, bool deeper) {
    for (int i = 1; i < points.length; i++) {
      var prePoint = points[i - 1];
      canvas.drawPath(
        Path()
          ..moveTo(prePoint.point.dx, points[i - 1].point.dy)
          ..quadraticBezierTo(prePoint.point.dx, prePoint.point.dy,
              points[i].point.dx, points[i].point.dy)
        // ..lineTo(points[i].point.dx, points[i].point.dy)
        ,
        paint.copyWith(strokeWidth: prePoint.width, blendMode: BlendMode.src),
      );
    }
  }

  @override
  SmoothLineNew copy() => SmoothLineNew(brushPrecision: brushPrecision);

  @override
  bool isIntersectingRectangle(Rect rect) {
    bool isIntersecting = false;
    for (var element in points) {
      if (element.point.dx > rect.left &&
          element.point.dx < rect.right &&
          element.point.dy > rect.top &&
          element.point.dy < rect.bottom) {
        isIntersecting = true;
        break;
      }
    }
    return isIntersecting;
  }

  @override
  Map<String, dynamic> toContentJson() {
    return <String, dynamic>{
      'brushPrecision': brushPrecision,
      'points': points.map((TimeOffset e) => e.toJson()).toList(),
      'paint': paint.toJson(),
    };
  }
}
