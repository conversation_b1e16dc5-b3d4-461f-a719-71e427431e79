import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:easy_white_board/common/config/storage_manager.dart';
import 'package:easy_white_board/common/extension/build_context_ext.dart';
import 'package:easy_white_board/log/log.dart';
import 'package:easy_white_board/model/db/drawing_data.dart';
import 'package:easy_white_board/model/db/service/content_painter_service.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/paint_content.dart';
import 'package:easy_white_board/page/drawing_board/plugin/paint_contents/signature/signature_line.dart';
import 'package:easy_white_board/page/drawing_board/widgets/tool_item_setting_menu.dart';
import 'package:easy_white_board/provider/business/bg_provider.dart';
import 'package:easy_white_board/resource/shared_preferences_keys.dart';
import 'package:easy_white_board/utils/ui_util.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../common/config/router_manager.dart';
import '../../provider/business/drawing_data_provider.dart';
import 'plugin/drawing_controller.dart';
import 'plugin/model/painter_style.dart';
import 'plugin/paint_contents/simple_line.dart';
import 'widgets/bg_item_setting_menu.dart';

class BoarderConfig {
  static const double zoomFactor = 1;

  static const bgSizeFactor = 20;
  static double bgWidth = bgSizeFactor * 1.sw;
  static double bgHeight = bgSizeFactor * 1.sh;
}

class DrawingBoardPageController {
  /// 绘制控制器
  late final DrawingController drawingController;
  late final TransformationController transformationController;

  final WidgetRef ref;

  final GlobalKey boardKey = GlobalKey();

  final Function(VoidCallback fn) setState;

  DrawingData? data;

  double xTranslate = 0.0;

  double yTranslate = 0.0;

  late DrawingDataNotifier drawingDataNotifier;

  late ContentPainterService contentPainterService;
  late SharedPreferences sharedPreferences;

  DrawingBoardPageController({required this.ref, required this.setState}) {
    contentPainterService = ContentPainterService();
    sharedPreferences = StorageManager.sharedPreferences;

    //加载默认笔
    PaintContent pen = loadDefaultPen();
    PainterStyle style = loadPainterStyle(pen.runtimeType);
    Logger.info("============style: ${style.color.toString()}=");

    drawingController = DrawingController(
        config: DrawConfig.def(
            contentType: pen.runtimeType,
            strokeWidth: style.strokeWidth,
            color: style.color),
        content: pen);
    transformationController = TransformationController();
    drawingDataNotifier = ref.read(drawingDataProvider.notifier);
  }

  void initTransform() {
    xTranslate = BoarderConfig.bgWidth * BoarderConfig.zoomFactor / 2;
    yTranslate = BoarderConfig.bgHeight * BoarderConfig.zoomFactor / 2;
    transformationController.value.setEntry(0, 0, BoarderConfig.zoomFactor);
    transformationController.value.setEntry(1, 1, BoarderConfig.zoomFactor);
    transformationController.value.setEntry(0, 3, -xTranslate);
    transformationController.value.setEntry(1, 3, -yTranslate);
  }

  bool isPen(Type currentType) {
    return currentType == SignatureLine || currentType == SimpleLine;
  }

  void doMove() {
    double xdistance = 1.sw / BoarderConfig.zoomFactor;
    double newX = xTranslate + xdistance;
    if (newX < 1.sw - xdistance) {
      moveBoard(-newX, yTranslate);
    } else {
      newX = xTranslate - xdistance;
      if (newX > xdistance) {
        moveBoard(newX, yTranslate);
      }
    }
  }

  void moveBoard(double x, double y) {
    transformationController.value.setEntry(0, 3, x);
    transformationController.value.setEntry(1, 3, -y);
  }

  void initDrawingData(int? id) {
    drawingController.clear();
    id ??= StorageManager.sharedPreferences
        .getInt(SharedPreferencesKeys.currentDrawingId);

    if (id != null) {
      //更新背景
      Future.delayed(const Duration(milliseconds: 100), () {
        ref.read(bgProvider.notifier).loadSavedBg(id!);
      });
      //更新数据
      data = drawingDataNotifier.getDrawingData(id);
      if (data != null) {
        List<PaintContent> paintContents = [];
        List<dynamic> jsons = jsonDecode(data!.content);
        if (jsons.isNotEmpty) {
          for (var element in jsons) {
            var paintContent = PaintContentHelper.fromJson(element);
            if (paintContent != null) {
              paintContents.add(paintContent);
            }
          }
          if (paintContents.isNotEmpty) {
            Logger.info("=======  paintContents:$paintContents");
            drawingController.addContents(paintContents);
          }
        }
      }
    }
  }

  void toHome(BuildContext context) {
    showLoading(msg: context.string.onSaving);
    saveDrawingData().then((value) {
      StorageManager.sharedPreferences
          .remove(SharedPreferencesKeys.currentDrawingId);
      toPage(
        RouteName.home,
        transition: TransitionType.custom,
        transitionBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child,
        ) =>
            Align(
          child: SizeTransition(
            sizeFactor: animation,
            axisAlignment: 1.0,
            child: child,
          ),
        ),
      );
    }).whenComplete(() {
      dismissLoading();
    });
  }

  Future onInactive(BuildContext context) async {
    Logger.info("====== on back");
    await saveDrawingData().then((DrawingData? needSaveData) {
      if (needSaveData != null) {
        StorageManager.sharedPreferences
            .setInt(SharedPreferencesKeys.currentDrawingId, needSaveData.id!);
      }
    });
  }

  void disposeController() {
    drawingController.dispose();
    transformationController.dispose();
  }

  /// 获取缩略图
  Future<ByteData?> getThumbnailImageData() async {
    try {
      final RenderRepaintBoundary boundary =
          boardKey.currentContext!.findRenderObject()! as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 0.8);

      return await image.toByteData(format: ui.ImageByteFormat.png);
    } catch (e) {
      debugPrint('获取图片数据出错:$e');
      return null;
    }
  }

  bool isSaving = false;
  /**
   * 保存
   */
  Future<DrawingData?> saveDrawingData() async {
    if (isSaving) {
      return null;
    }
    try {
      List<Map<String, dynamic>> jsons = drawingController.getJsonList();
      if (jsons.isNotEmpty || data != null) {
        ByteData? imageData = await drawingController.getThumbnailImageData();
        Uint8List? imageDatalist = imageData?.buffer.asUint8List();
        String? imageStr;
        if (imageDatalist != null) {
          imageStr = base64.encode(imageDatalist);
        }
        String imageJsonContent = jsonEncode(jsons);
        var now = DateTime.now();
        int currentTimestamp = now.millisecondsSinceEpoch;
        DrawingData needSaveData;
        var bg = ref.read(bgProvider);
        final data = this.data;
        if (data != null) {
          data.updatedTime = currentTimestamp;
          data.content = imageJsonContent;
          if (imageStr != null) {
            data.imageStr = imageStr;
          }
          data.bgType = bg.bgType;
          data.bgColor = bg.bgColor.toARGB32();
          needSaveData = data;
        } else {
          needSaveData = DrawingData(
              content: imageJsonContent,
              createdTime: currentTimestamp,
              updatedTime: currentTimestamp,
              imageStr: imageStr,
              bgType: bg.bgType,
              bgColor: bg.bgColor.toARGB32());
        }
        drawingDataNotifier.saveDrawingData(needSaveData);

        Logger.info("=========== save data");
        return needSaveData;
      }
    } finally {
      isSaving = false;
    }
    return null;
  }

  void toCenter() {
    initTransform();
    refreshState();
  }

  refreshState() {
    setState(() {});
  }

  void showBgConfigMenu() {
    showSmartDialog(
        BgItemSettingMenu(
          controller: this,
        ),
        clickMaskDismiss: true,
        alignment: Alignment.bottomCenter);
  }

  setPen(
    BuildContext c,
    Type currType,
  ) {
    SmartDialogController controller = SmartDialogController();
    PaintContent line = loadDefaultPen();
    //展示粗细，颜色，笔刷选框
    if (isPen(currType)) {
      //当前选中笔了
      showAttach(
        targetContext: c,
        controller: controller,
        maskColor: Colors.transparent,
        alignment: Alignment.topCenter,
        widget: ToolItemSettingMenu(
          content: line,
          controller: this,
        ),
      );
    } else {
      //当前没有选中笔
      PainterStyle? style =
          contentPainterService.loadPainterStyle(line.runtimeType);
      drawingController.setPaintContent(line, style: style);
    }
  }

  setPaintContent(
      BuildContext c, Type currType, PaintContent content, bool isChecked) {
    Logger.info("currentType:$currType, content:$content");
    //展示粗细，颜色，笔刷选框
    if (isChecked) {
      //已经checked了
      showAttach(
          targetContext: c,
          maskColor: Colors.transparent,
          alignment: Alignment.topCenter,
          widget: ToolItemSettingMenu(
            content: content,
            controller: this,
          ));
    } else {
      PainterStyle? style =
          contentPainterService.loadPainterStyle(content.runtimeType);
      drawingController.setPaintContent(content, style: style);
    }
  }

  void updateSizeChange(PaintContent content, double width) {
    savePainterConfig(content, strokeWidth: width);
  }

  void updateColorChange(PaintContent content, Color color) {
    savePainterConfig(content, color: color);
  }

  /**
   * 更新画笔
   */
  void updatePenChange(PaintContent pen) {
    sharedPreferences.setString(
        SharedPreferencesKeys.defaultPenType, pen.runtimeType.toString());
    //更新颜色和宽度
    //保存数据
    PainterStyle style = contentPainterService.savePainerConfig(
        pen.runtimeType,
        drawingController.drawConfig.value.strokeWidth,
        drawingController.drawConfig.value.color);
    drawingController.setPaintContent(pen);
  }

  void savePainterConfig(PaintContent content,
      {double? strokeWidth, Color? color}) {
    //保存数据
    PainterStyle style = contentPainterService.savePainerConfig(
        content.runtimeType, strokeWidth, color);

    //更新配置
    drawingController.setPaintStyle(content, style);
  }

  PainterStyle loadPainterStyle(Type contentType) {
    return contentPainterService.loadPainterStyle(contentType);
  }

  /**
   * 加载默认的笔
   */
  PaintContent loadDefaultPen() {
    //查询默认的笔设置
    String defaultPen =
        sharedPreferences.getString(SharedPreferencesKeys.defaultPenType) ??
            "SignatureLine";
    Logger.info("defaultPen:$defaultPen");
    PaintContent line = SignatureLine();
    if (defaultPen != line.runtimeType.toString()) {
      line = SimpleLine();
    }
    return line;
  }

  void updateBg(String? runType, Color? bg) {
    ref.read(bgProvider.notifier).updateBg(runType, bg);
  }

  BgData loadDefaultBoarderBg() {
    return ref.read(bgProvider);
  }

  Future doExport(BuildContext context) async {
    showLoading(msg: context.string.onSaving);
    await _doExport();
    dismissLoading();
  }

  Future _doExport() async {
    ByteData? imageData = await drawingController.getImageData();
    if (imageData != null) {
      await PhotoManager.plugin.saveImage(imageData.buffer.asUint8List(),
          filename: "${DateTime.now().millisecond.toString()}.png");
    }
  }
}
