



import 'package:flutter/foundation.dart';

import 'env/env_config.dart';

class BuildConfig{

  /// 是否是生产环境
  static const bool isProduction = kReleaseMode;

  ///环境信息 如:debug test release
  static final EnvConfig envConfig = Env.envConfig;


  static late String versionName;
  static late String buildNumber;
  static late String appName='白板大师-随手涂鸦草稿';
  static late String packageName;
  static late String channelName;
  static late String sdkVersion;



}