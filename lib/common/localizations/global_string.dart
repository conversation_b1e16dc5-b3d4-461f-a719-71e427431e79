///   author : liuduo
///   e-mail : <EMAIL>
///   time   : 2022/07/27
///   desc   : 多语言中文
///   version: 1.0
class GlobalString {
  String cancel="Cancel";
  String confirm="Confirm";
  String delete='Delete';

  String untitled='Untitled';

  String welcomeToUse='Welcome to ';
  String confirmToDelete='Confirm to delete?';
  String renameDrawing='Rename';
  String nameCouldNotEmpty='Name could not be empty!';

  String onSaving='Saving...';

  String handWriting='handWriting';

  String strokeWidth='strokeWidth';








}
