

import 'dart:ui';

import 'package:easy_white_board/log/log.dart';

import '../../../common/config/storage_manager.dart';
import '../../../objectbox.g.dart';
import '../../../page/drawing_board/plugin/model/painter_style.dart';
import '../../../page/drawing_board/plugin/paint_contents/paint_content.dart';
import '../content_painter_style_data.dart';
import 'base_service.dart';

class ContentPainterService extends BaseService<ContentPainterStyleData>{

  ContentPainterStyleData? loadPainterData(String type){
    return dataStore.query(ContentPainterStyleData_.runType.equals(type)).build().findFirst();
  }

  PainterStyle loadPainterStyle(Type contentType){
    String type=contentType.toString();
    var data= loadPainterData(type);
    return PainterStyle(color: data!=null?Color(data.color):PainterStyle.defaultColor,strokeWidth: data?.width??PainterStyle.defaultWidth);
  }

  PainterStyle savePainerConfig(Type contentType,double? strokeWidth,Color? color){
    //保存数据
    String type=contentType.toString();
    Logger.info("save color:type:$type, color:$color");
    int now=DateTime.now().millisecondsSinceEpoch;
    ContentPainterStyleData styleData=loadPainterData(type)??ContentPainterStyleData(runType: type, createdTime: now, updatedTime: now);
    if(strokeWidth!=null){
      styleData.width=strokeWidth;
    }
    if(color!=null){
      styleData.color=color.value;
    }
    styleData.updatedTime=now;
    dataStore.put(styleData);
    return PainterStyle(strokeWidth: styleData.width,color: Color(styleData.color));
  }
}