

import 'package:easy_white_board/provider/business/bg_provider.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';


part 'drawing_data.g.dart';




@JsonSerializable()
@Entity()
class DrawingData {

  @Id()
  int? id;

  String? name;

  String content;

  String? imageStr;

  int createdTime;

  int updatedTime;

  String? username;

  String bgType;

  late int bgColor;

  DrawingData({this.id, required this.content, required this.createdTime,required this.updatedTime, this.username,   this.name, this.imageStr,  this.bgType=BgData.defaultType, int? bgColor}){
    this.bgColor=bgColor??BgData.defaultColor.value;
  }

  factory DrawingData.fromJson(Map<String, dynamic> json) => _$DrawingDataFromJson(json);
  Map<String, dynamic> toJson() => _$DrawingDataToJson(this);
}