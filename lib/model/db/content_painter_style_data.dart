



import 'package:easy_white_board/page/drawing_board/plugin/model/painter_style.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:objectbox/objectbox.dart';

part 'content_painter_style_data.g.dart';


@JsonSerializable()
@Entity()
class ContentPainterStyleData {

  @Id()
  int? id;

  String runType;

  late int color;

  double width;

  int createdTime;

  int updatedTime;

  String? extraConfig;


  ContentPainterStyleData(
      {this.id,
      required this.runType,
      int? color,
      this.width=PainterStyle.defaultWidth,
        this.extraConfig,
      required this.createdTime,
      required this.updatedTime}){
    this.color=color??PainterStyle.defaultColor.value;
  }

  factory ContentPainterStyleData.fromJson(Map<String, dynamic> json) => _$ContentPainterStyleDataFromJson(json);
  Map<String, dynamic> toJson() => _$ContentPainterStyleDataToJson(this);
}